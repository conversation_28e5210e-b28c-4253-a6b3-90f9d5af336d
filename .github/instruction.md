# Goal
Generate a tailored resume by executing a structured research and content optimization workflow.
Use Perplexity for research and prioritize factual accuracy.

## Step 1: Job Analysis
1. **Research job requirements**
   - Scrape job description: `[<PERSON> URL]`
   - Analyze Glassdoor company reviews
   - Investigate hiring manager: `[LinkedIn URL]`
   - Save findings to `background-knowledge.md`

2. **Extract key criteria**
   - Identify 3-5 hard skills from JD
   - Note 2-3 soft skills emphasized
   - Highlight company values/philosophy

## Step 2: Resume Strategy
1. **Create optimization plan**
   - Cross-reference with:
     - `_userBackground/fullResume.md`
     - `_userBackground/keyStrength.md`
   - Output: `resume-plan.md` containing:
     - Priority skills to highlight
     - Irrelevant sections to remove
     - Achievement rewrite targets

2. **Content alignment rules**
   - Use STAR method for project descriptions
   - Quantify achievements (e.g., "Improved model accuracy by 15%")
   - Mirror JD keywords in skills section

## Step 3: Resume Generation
1. **Produce tailored draft**
   - Input: `resume-plan.md` + `background-knowledge.md`
   - Output: `tailored-resume.md` with:
     - 1-page strict length
     - JD-matched professional summary
     - Reordered experience sections

2. **Optimization checks**
   - Verify ATS compatibility
   - Ensure keyword density matches JD
   - Confirm availability date: `[DATE]`

## Step 4: Interview Prep
1. **Generate question bank**
   - Research role-specific questions
   - Include technical/behavioral variants
   - Output: `interview-prep.md`
