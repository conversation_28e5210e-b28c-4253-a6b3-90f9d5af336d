# Goal

To generate a tailor-made resume and comprehensive interview preparation based on the given job information, by first creating a todo plan here.
For research, please use the perplexity tool.

# TODO 1 - Tailor the resume for the job application

1. [ ] Gather the job requirement and save the findings and insights into `background-knowledge.md`
   - [ ] Job description: [REPLACE_WITH_JOB_LINK]
   - [ ] Glassdoor reviews about the job and the company
   - [ ] Gather information about the hiring manager: [REPLACE_WITH_HIRING_MANAGER_LINK if available]
   - [ ] Research company culture, values, and recent news
   - [ ] Identify key technologies and skills mentioned in the job posting
   - [ ] Understand the company's products/services and target market

2. [ ] Based on the `background-knowledge.md`, create a plan to tailor the resume as `resume-plan.md`
   - [ ] Research on the best candidates related to the job description
   - [ ] Identify key skills and experiences that match the job requirements
   - [ ] Highlight relevant projects and achievements from the user's original resume
   - [ ] Remove or downplay less relevant experiences
   - [ ] Ensure the resume format is professional and easy to read
   - [ ] Map user's experience to specific job requirements
   - [ ] Identify gaps and suggest ways to address them

3. [ ] Based on the `resume-plan.md`, create a tailored resume as `tailored-resume.md` and make sure it's aligned with the facts gathered from the following files:
    - User's original resume: `_userBackground/fullResume.md`
    - Users' strength: `_userBackground/keyStrength.md`
    - Background knowledge about the company: `background-knowledge.md`
    - Availability: [REPLACE_WITH_AVAILABILITY_DATE]
    - [ ] Customize summary/objective to match the role
    - [ ] Reorder and emphasize relevant experience
    - [ ] Use keywords from the job description
    - [ ] Quantify achievements where possible

4. [ ] Fine tune for the job application and create `fine-tuned-resume.md`
   - [ ] Review the tailored resume for any final adjustments
   - [ ] Trim down the resume to fit within 1-2 pages
   - [ ] Focus more on the most relevant experience for the specific role
   - [ ] Ensure consistent formatting and professional appearance
   - [ ] Proofread for grammar and spelling errors

# TODO 2 - Understand the interview process

1. [ ] Research the interview process and save the findings into `interviews.md`
   - [ ] Reference similar jobs like what's described in the `background-knowledge.md`
   - [ ] Research using perplexity tool on past interview questions, from both Chinese and English sources
   - [ ] Find out about the interview format (e.g., technical interviews, coding tests, behavioral interviews)
   - [ ] Research typical interview rounds for similar positions at similar companies
   - [ ] Identify common technical challenges and coding problems
   - [ ] Understand the company's interview philosophy and culture

2. [ ] Create comprehensive preparation guide as `interview-prep.md`
   - [ ] Add relevant emojis to all H2 headings for better visual organization
   - [ ] Create company-specific interview process section with recruiter insights (if available)
   - [ ] Include detailed technical challenges with sample code implementations
   - [ ] Add preparation strategy and practice recommendations with checkboxes
   - [ ] Create interview day tips section with actionable advice
   - [ ] Build comprehensive progress tracker with overall readiness checklist
   - [ ] Include typical interview process overview (multi-round structure)
   - [ ] Add key technical areas to master with practical examples
   - [ ] Create common technical interview questions with relevant keywords
   - [ ] Build comprehensive glossary of technical keywords organized by category
   - [ ] Include coding interview focus areas and behavioral interview preparation
   - [ ] Add company-specific preparation section and study resources
   - [ ] Create success factors and final interview tips sections
   - [ ] Review document to remove duplications and ensure consistency
   - [ ] Standardize formatting with consistent checkbox bullet points throughout

3. [ ] Integrate recruiter insights into the preparation guide (if available)
   - [ ] Add specific technical challenges mentioned by recruiters
   - [ ] Include preferred programming languages and frameworks
   - [ ] Document typical interview flow and expectations
   - [ ] Add any insider tips or company-specific advice
   - [ ] Include sample code structures for common challenges

# TODO 3 - Additional preparation materials (optional)

1. [ ] Create a cover letter template as `cover-letter.md`
   - [ ] Research the company's mission and values
   - [ ] Customize the letter to the specific role and company
   - [ ] Highlight key achievements that match job requirements
   - [ ] Keep it concise (1 page maximum)

2. [ ] Prepare for salary negotiation
   - [ ] Research salary ranges for similar positions in the area
   - [ ] Understand the company's compensation structure
   - [ ] Prepare justification for desired salary range

3. [ ] Create a portfolio showcase (if applicable)
   - [ ] Select relevant projects that demonstrate required skills
   - [ ] Prepare concise project descriptions and outcomes
   - [ ] Ensure all code examples are clean and well-documented

# File Structure Expected:
```
[company-name]/
├── todo.md (this file)
├── background-knowledge.md
├── resume-plan.md
├── tailored-resume.md
├── fine-tuned-resume.md
├── interviews.md
├── interview-prep.md
├── cover-letter.md (optional)
└── _userBackground/
    ├── fullResume.md
    ├── keyStrength.md
    └── glossary.md
```

# Notes for AI Assistant:
- Use perplexity tool for research whenever possible
- Maintain consistent formatting with checkboxes throughout
- Add emojis to major headings for better visual organization
- Create comprehensive, actionable content with specific examples
- Ensure all advice is tailored to the specific company and role
- Include progress tracking mechanisms
- Remove duplications and maintain consistency
- Provide practical code examples for technical roles
- Create glossaries for technical terms
- Build both company-specific and general interview preparation content

# Customization Instructions:
1. Replace [REPLACE_WITH_JOB_LINK] with the actual job posting URL
2. Replace [REPLACE_WITH_HIRING_MANAGER_LINK] with LinkedIn profile if available
3. Replace [REPLACE_WITH_AVAILABILITY_DATE] with candidate's availability
4. Adjust technical focus areas based on the specific role (AI/ML, Frontend, Backend, etc.)
5. Modify interview preparation sections based on role requirements
6. Add role-specific coding challenges and technical concepts
7. Customize the timeline based on application deadlines
