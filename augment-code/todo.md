# Goal

To generate a tailor-made resume based on the given information, by first creating a todo plan here.
For research, please use the perplexity tool.

# TODO 1 - Tailor the resume for the job application

1. [x] Gather the job requirement and save the findings and insights into `background-knowledge.md`
   - [x] Job description: [Job Application for Applied AI Engineer at Augment Code](https://job-boards.greenhouse.io/augmentcomputing/jobs/4680036008)
   - [x] Glassdoor reviews about the job and the company
   - [x] Gather information about the hiring manager: [(32) Vinay Perneti | LinkedIn](https://www.linkedin.com/in/vinayperneti/)

2. [x] Based on the `background-knowledge.md`, create a plan to tailor the resume as `resume-plan.md`
   - [x] Research on the best candidates related to the job description
   - [x] Identify key skills and experiences that match the job requirements
   - [x] Highlight relevant projects and achievements from the user's original resume
   - [x] Remove or downplay less relevant experiences
   - [x] Ensure the resume format is professional and easy to read

3. [x] Based on the `resume-plan.md`, create a tailored resume as `tailored-resume.md` and make sure it's aligned with the facts gathered from the following files:
    - User's original resume: `_userBackground/fullResume.md`
    - Users' strength: `_userBackground/keyStrength.md`
    - Background knowledge about the company: `background-knowledge.md`
    - Availability: Late July 2025

4. [x] Fine tune for the job applicaiton and create `fine-tuned-resume.md`
   - [x] Review the tailored resume for any final adjustments
   - [x] Trim down the resume to fit within 1-2 pages, focus more on the end-to-end LLM app development experience

# TODO 2 - Understand the interview process

1. [x] Research the interview process and save the findings into `interviews.md`
   - reference simiar job like what's described in the `background-knowledge.md`
   - research using perplexity tool on past interview quetsions, from both Chinese and English sources
   - Find out about the interview format (e.g., technical interviews, coding tests, behavioral interviews)

2. [x] Create comprehensive preparation guide as `interview-prep.md`
   - [x] Add relevant emojis to all H2 headings for better visual organization
   - [x] Create company-specific interview process section with recruiter insights
   - [x] Add preparation strategy and practice recommendations with checkboxes
   - [x] Create interview day tips section with actionable advice
   - [x] Build comprehensive progress tracker with overall readiness checklist
   - [x] Include typical interview process overview (5-round structure)
   - [x] Add key technical areas to master with practical examples
   - [x] Create common technical interview questions with relevant keywords
   - [x] Build comprehensive glossary of technical keywords organized by category
   - [x] Include coding interview focus areas and behavioral interview preparation
   - [x] Add company-specific preparation section and study resources
   - [x] Create success factors and final interview tips sections
   - [x] Review document to remove duplications and ensure consistency
   - [x] Standardize formatting with consistent checkbox bullet points throughout

3. [x] Integrate recruiter insights into the preparation guide
```
To give you an example, for the front end developer role, I can share that this is typically the process that they follow:

1. First stage is usually a coding /technical exercise with one of the senior engineers. This is usually a "Promises Question". I googled this and came up with the following article, but I am not sure if this helps at all

FAANG’s Favourite Promises Question | by Ritika Rana | Medium

https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise

I believe in this instance, candidates will be implementing a Promise API, around creating a customized Promise class using Javascript. .

2. The onsite interview then involves:

Webcrawler - one of the technical tests is around webcrawlers. You will be presented with a question and will have to create the solution and implement it, bug free. I haven't been given the exact question, but it could be something like "Implement a BFS/DFS algorithm to crawl a website starting from a given URL" They do have a strong preference for Typescript and Javascript languages. So it would be good to spend some beforehand looking at the structure of webcrawlers etc

JSON - this is usually a JSON Viewer question and again you need to implement a working viewer. Be aware of the recursive nature of the component  and try to avoid writing components for specific data types that don't handle basic JSON syntax very well

I believe for this AI product engineer Tech Lead role, as it is fullstack, they would most likely use one of the technical exercises above and for a second stage, it would be more around prompts.

I totally understand that different companies use different types of interviews etc

I know you are very keen on Augment Code and rightly so, it's a great product and great team.
```