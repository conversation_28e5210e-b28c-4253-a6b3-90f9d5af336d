# Ka <PERSON> Shum

**Staff Software Engineer | End-to-End LLM Application Developer**

San Francisco Bay Area  
+1 415 568 8492 · <EMAIL>  
[LinkedIn: kyshum](https://www.linkedin.com/in/kyshum)

---

## Summary

Staff Software Engineer specializing in **end-to-end LLM application development** and production-scale GenAI solutions. Led development of **Account IQ, LinkedIn's first enterprise GenAI product**, architecting the complete AI pipeline from prompt engineering and model integration to user interface and performance optimization, achieving **17% growth in weekly active users**.

Expert in **full-stack LLM integration**—from backend AI service architecture and prompt optimization to frontend AI-powered interfaces and real-time user experiences. Proven track record of translating complex AI capabilities into intuitive, reliable developer tools that enhance productivity at scale.

---

## Experience

### LinkedIn
**Staff Software Engineer** | *April 2025 – Present*  
**Senior Software Engineer, Sales Navigator** | *October 2020 – April 2025*

#### End-to-End LLM Application Development

**Account IQ - Complete GenAI Product Pipeline**
- **Architected full-stack LLM application** as sole front-end engineer, implementing end-to-end AI pipeline from model integration to user interface
- **Built comprehensive prompt engineering system** with iterative optimization, A/B testing, and performance monitoring for production LLM workflows
- **Implemented real-time AI processing pipeline** integrating multiple LLM APIs (OpenAI, Claude, Gemini) with intelligent routing and fallback mechanisms
- **Achieved 17% increase in Weekly Active Users** through seamless AI feature integration across search, homepage, and lead discovery interfaces

**Advanced AI Feature Development**
- **Developed Prompt IDE** with batch processing, template management, and automated prompt optimization for rapid LLM experimentation
- **Built AI-assisted search (AIAS)** with natural language query processing, semantic understanding, and contextual result ranking
- **Created AI-powered messaging system (Kiwi)** with inline content generation, tone optimization, and personalization algorithms
- **Implemented comprehensive AI evaluation framework** with qualitative feedback loops, quantitative performance metrics, and automated quality assessment

#### Production-Scale AI Infrastructure

**LLM Integration & Performance**
- **Designed scalable AI service architecture** handling 3M+ daily visitors with sub-second response times and 99.9% availability
- **Built robust error handling and retry logic** for LLM service failures, ensuring graceful degradation and user experience continuity
- **Implemented AI model evaluation pipeline** with automated testing, bias detection, and performance benchmarking
- **Created reusable AI component library** with pre-built LLM integrations, enabling rapid AI feature development across teams

**Developer Tools & AI Productivity**
- **Built AI-powered CRM integration platform** serving **13,000 daily users** with intelligent data synchronization and conflict resolution
- **Developed automated prompt testing framework** with regression testing, performance monitoring, and continuous integration
- **Led incremental migration of 200k+ LOC codebase to TypeScript** improving AI system reliability and developer productivity

#### Technical Leadership in AI Development

**Cross-Functional AI Strategy**
- **Served as AI technical lead** for cross-team initiatives, establishing best practices for LLM integration and evaluation
- **Mentored engineers** on prompt engineering, AI system design, and production AI deployment patterns
- **Partnered with Product and Design teams** to define AI UX patterns and establish human-AI interaction guidelines

---

### Sage | San Francisco Bay Area
**Software Engineer** | *July 2015 – October 2018*

- Built full-stack applications with data visualization and user analytics integration
- Developed REST APIs and database systems supporting thousands of concurrent users
- Led frontend architecture migration from AngularJS to React.js with zero downtime

---

## Core AI & Technical Skills

**LLM Application Development**
- Large Language Model Integration (OpenAI, Claude, Gemini)
- Advanced Prompt Engineering & Optimization
- AI Model Evaluation & Testing Frameworks
- LangChain & AI Workflow Orchestration

**Full-Stack Development**
- TypeScript, JavaScript, React, Node.js
- REST APIs & Microservices Architecture
- Real-time Systems & WebSocket Integration
- Database Design & Performance Optimization

**AI Infrastructure & DevOps**
- Production AI Service Deployment
- AI Model Performance Monitoring
- Automated Testing for AI Systems
- API Integration & Service Architecture

---

## Education & Certifications

**The Hong Kong University of Science and Technology**  
*Bachelor's Degree, Computer Science* | *2012 – 2016*  
**School of Engineering Scholarship** recipient

**Relevant Certifications:**
- Big Data Fundamentals with PySpark
- Web Security and Access Management: JWT, OAuth2 & OpenId Connect
- Writing Efficient Python Code

---

## Key Achievements

- **Led development of LinkedIn's first enterprise GenAI product** with measurable business impact
- **Achieved 17% growth in AI feature adoption** through end-to-end LLM application development
- **Built AI systems serving 13,000+ daily users** with high reliability and performance standards
- **Created reusable AI development frameworks** adopted across multiple engineering teams

---

**Languages:** English (Full Professional) · Mandarin Chinese (Native) · Cantonese (Native)  
**Availability:** Late July 2025
