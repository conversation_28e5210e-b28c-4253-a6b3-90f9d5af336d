# <PERSON> <PERSON> Shum

**Staff Software Engineer @ LinkedIn | Gen-AI**

San Francisco Bay Area
+1 415 568 8492 · <EMAIL>
[LinkedIn: kyshum](https://www.linkedin.com/in/kyshum)

---

## Summary

As a Staff Software Engineer at LinkedIn, I architect and deliver GenAI-powered solutions that transform how sales professionals connect, qualify, and build relationships at scale. My strengths span end-to-end product development, technical leadership, and developer productivity—impacting millions of users daily.

I spearheaded the launch of Account IQ, LinkedIn Sales Navigator's first GenAI feature leveraging LLMs, driving double-digit growth in weekly active users and setting the foundation for future AI innovation. My work includes building and scaling high-impact features such as CRM workflow integrations, Smart Links, and TeamLink, empowering 40,000+ daily users with seamless prospecting and engagement tools.

I am deeply invested in developer excellence, having led the incremental migration of a 200k+ LOC codebase to TypeScript for 3M+ daily visitors, and driving automation and code quality through advanced ESLint rules and cross-team tooling. My approach blends hands-on technical expertise (TypeScript, React, Node.js, GenAI, CRM APIs, SEO, Ember.js) with a collaborative, data-driven mindset.

---

## Experience

### LinkedIn
**Staff Software Engineer** | *April 2025 – Present*
**Senior Software Engineer, Sales Navigator** | *October 2020 – April 2025*
**Software Engineer** | *October 2018 – October 2020*

#### Key Achievements - Account IQ & GenAI Leadership
- **Led LinkedIn's first enterprise generative AI product** (Account IQ) for sales professionals, serving as the sole front-end engineer
- **Achieved 17% increase in AIQ Weekly Active Users** through full-stack implementation of AI features across backend and frontend
- **Integrated AIQ across multiple entry points**, driving 4.2%–17% increase in WAU for Account IQ
- **Operated as full stack engineer**, implementing AIQ features in both backend (BE stack) and front-end, including search, homepage, and lead/company search integrations

#### AI Feature Development & Innovation
- **Handpicked for LSS Catalyst working group**, playing central role in launching GenAI features:
  - **AIAS (AI-assisted search)**: Coach Search UI and accessibility improvements
  - **Kiwi (AI-assisted messaging)**: Implemented AI message inline CTA
- **Built comprehensive tracking systems** for both qualitative and quantitative feedback for AI initiatives
- **Designed and implemented tracking** for major AI initiatives (AIAS, AIQ, Catalyst)
- **Developed Prompt IDE** with batch calls and innovation tooling

#### Technical Leadership & Architecture
- **Led incremental migration of 200k+ LOC codebase to TypeScript** for 3M+ daily visitors
- **Improved LSS site speed by 15%** through code cleanup and optimization
- **Authored new ESLint rules** to enforce code quality and error handling standards
- **Refactored eight major components** for new account page using CSS container queries
- **Led major refactor of CRM status & writeback features** with zero production disruption

#### Cross-Functional Leadership
- **Served as primary Point of Contact (POC)** for cross-team initiatives, streamlining AIQ partner onboarding process
- **Onboarded and mentored new engineers** including Nanda, Filbert Jahja, and Yang Du
- **Partnered with design** to establish best practices and agile RFCs for AIQ and GenAI features
- **Led postmortem and cleanup of overdue experiments** using Tslix tool, resulting in 23% of tickets closed within two months

#### Product Impact & User Growth
- **Built CRM badge and writeback dialog** now used by 13,000 daily users to sync data with Salesforce and Microsoft Dynamics CRM
- **Launched lighthouse-guest-web**, a new guest platform averaging 170,000 daily visits and ranking 8th among LSS's most visited pages
- **Implemented TeamLink Spotlight**, enhancing sellers' ability to identify and engage with trusted leads
- **Developed reusable onboarding tutorial component** for AIQ enabling real-time UX adjustments

---

### Sage | San Francisco Bay Area
**Software Engineer** | *July 2015 – October 2018*

- Led frontend development for Compass (acquired by Sage), implementing the flagship product using AngularJS
- Developed endpoints using Ruby and Sinatra
- Implemented data visualization with HighCharts.JS and tracking with Mixpanel
- Built a new Homepage from scratch using Vue.JS
- Helped migrate the flagship web app from AngularJS to React.JS
- Performed multiple database migrations and daily CRUD operations with PostgreSQL

---

## Education

**The Hong Kong University of Science and Technology**
*Bachelor's Degree, Computer Science* | *2012 – 2016*

**Korea Advanced Institute of Science and Technology**
*Exchange Programme, Computer Science, Industrial Design* | *2015*

---

## Skills

**AI & Development**
- LangChain
- Generative AI
- Large Language Models (LLMs)
- Prompt Engineering

**Programming & Technologies**
- TypeScript, React, Node.js, JavaScript
- Ember.js, Vue.js, AngularJS
- REST APIs, CRM APIs
- Ruby, Sinatra, PHP
- PostgreSQL, mySQL

**Specializations**
- W3C Accessibility
- SEO
- Data Visualization (HighCharts.JS)
- Performance Optimization

---

## Certifications

- Design Kit: The Course for Human-Centered Design – IDEO.org
- Big Data Fundamentals with PySpark
- Introduction to PySpark
- Web Security and Access Management: JWT, OAuth2 & OpenId Connect
- Writing Efficient Python Code

---

## Honors & Awards

- **School of Engineering Scholarship and University Scholarship** – HKUST
- **Audience Favourite Award** – HackUST (Project: Openlore – Cyber-learning and course content management platform)

---

## Languages

- Mandarin Chinese (Native or Bilingual)
- Cantonese (Native or Bilingual)
- English (Full Professional)

---

**Availability: Late July 2025**
