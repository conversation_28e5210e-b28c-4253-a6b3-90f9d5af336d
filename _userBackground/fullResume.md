# Ka <PERSON> Shum

**Staff Software Engineer @ LinkedIn | Gen-AI**
San Francisco Bay Area
+1 415 568 8492 · <EMAIL>
[LinkedIn: kyshum](https://www.linkedin.com/in/kyshum)

---

## Summary

As a Staff Software Engineer at LinkedIn, I architect and deliver GenAI-powered solutions that transform how sales professionals connect, qualify, and build relationships at scale. My strengths span end-to-end product development, technical leadership, and developer productivity—impacting millions of users daily.

I spearheaded the launch of Account IQ, LinkedIn Sales Navigator’s first GenAI feature leveraging LLMs, driving double-digit growth in weekly active users and setting the foundation for future AI innovation. My work includes building and scaling high-impact features such as CRM workflow integrations, Smart Links, and TeamLink, empowering 40,000+ daily users with seamless prospecting and engagement tools.

I am deeply invested in developer excellence, having led the incremental migration of a 200k+ LOC codebase to TypeScript for 3M+ daily visitors, and driving automation and code quality through advanced ESLint rules and cross-team tooling. My approach blends hands-on technical expertise (TypeScript, React, Node.js, GenAI, CRM APIs, SEO, Ember.js) with a collaborative, data-driven mindset.

Passionate about building robust, user-centric products and high-performing teams, I thrive at the intersection of GenAI, large-scale web architecture, and business impact[1].

---

## Experience

**LinkedIn**
*Staff Software Engineer*
_April 2025 – Present_

*Senior Software Engineer, Sales Navigator*
_October 2020 – April 2025_

*Software Engineer*
_October 2018 – October 2020_

---

**Sage, San Francisco Bay Area**
*Software Engineer*
_July 2015 – October 2018_

- Led frontend development for Compass (acquired by Sage), implementing the flagship product using AngularJS.
- Developed endpoints using Ruby and Sinatra.
- Implemented data visualization with HighCharts.JS and tracking with Mixpanel.
- Built a new Homepage from scratch using Vue.JS.
- Helped migrate the flagship web app from AngularJS to React.JS.
- Performed multiple database migrations and daily CRUD operations with PostgreSQL[1].

---

**FansWIFi, Hong Kong**
*Software Engineer*
_May 2016 – July 2016_

- Assisted in developing an admin platform using Node.JS and PHP.
- Implemented login flow in mobile device captive portal.
- Developed responsive webpages for various devices[1].

---

**KeptMe Pty Ltd, Hong Kong**
*Software Engineer*
_September 2015 – November 2015_

- Developed a template generator using React.JS to scale the customer base.
- Designed and implemented a PDF generator using PDF.JS[1].

---

**PCCW Teleservices, Hong Kong**
*Network Operations Engineer | Intern*
_July 2013 – August 2013_

- Participated in the development of web services for internal use, utilizing JavaScript, JSP, HTML+CSS, and mySQL[1].

---

## Education

**The Hong Kong University of Science and Technology**
*Bachelor's Degree, Computer Science*
_2012 – 2016_

**Korea Advanced Institute of Science and Technology**
*Exchange Programme, Computer Science, Industrial Design*
_2015_

---

## Skills

- LangChain
- W3C Accessibility
- Generative AI
- TypeScript, React, Node.js, Ember.js, JavaScript, REST APIs, SEO

---

## Languages

- Mandarin Chinese (Native or Bilingual)
- Cantonese (Native or Bilingual)
- English (Full Professional)[1]

---

## Certifications

- Design Kit: The Course for Human-Centered Design – IDEO.org
- Big Data Fundamentals with PySpark
- Introduction to PySpark
- Web Security and Access Management: JWT, OAuth2 & OpenId Connect
- Writing Efficient Python Code[1]

---

## Honors & Awards

- **School of Engineering Scholarship and University Scholarship** – HKUST
- **Audience Favourite Award** – HackUST (Project: Openlore – Cyber-learning and course content management platform)[1]

---

## Recommendations

> “Alan started working for me as part of our internship program. Over the years I watched him grow into a capable and professional full stack web developer. Alan always approached his tasks with enthusiasm and dedication, he collaborated well with his team members and was always looking for an optimal solution whilst balancing implementation tradeoffs. I am proud of the growth Alan demonstrated while working with him and would happily recommend him to anyone looking for a first-rate Software Engineer.”
> — David Dickson[1]

[1] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/10682027/29fa756b-111e-491f-b1c3-3b6531b65d95/Profile-2.pdf
[2] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/10682027/8a3a8927-e8ed-451c-bc4c-91f05c1e95b5/Resume-2024_09_24.pdf