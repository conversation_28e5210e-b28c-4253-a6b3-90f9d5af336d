# Glossary of Terms and Acronyms

- LSS - LinkedIn Sales Solutions
- AIQ - Acocunt IQ - The first LinkedIn's enterirpise generative AI product for sales professionals that gathers information from various sources to provide a quick summary of a company, aiming to help sales professionals to prepare for meetings and understand their prospects better.
- GenAI - Generative AI
- POC (context: "POC for cross teams") - Point of Contact
- Account IQ - AIQ
- onboarding intake form - A form to automatically onboard new use cases of Account IQ, to we could track the usages, measure the success and scale as needed
- lighthouse-web / lighthouse-guest-web - internal codebase name of the frontend of LinkedIn Sales Solutions
- Tslix - LinkedIn's internal tool that tracks overdue AB tests and experiments, for engineers to clean up unused code
- JIRA tickets - Atlassian's issue and project tracking software
- lixes / overdue lixes - LinkedIn's internal tool used for conducting AB tests and experiments
- MT (context: "LSS MT") - MT stands for mid-tier, a layer that serves and handle business logic between the frontend and backend services
- Widget (context: "LSS Widget") - Widgets are what LSS integrates into other third-party applications like Salesforce or Microsoft Dynamics CRM, allowing users to access LinkedIn Sales Navigator features directly within those platforms.
- UMP dataset - User Metrics Platform dataset, a dataset that tracks user interactions and metrics across LinkedIn's products, including Sales Navigator.
- AIAS - AI asisted search, a feature in LinkedIn Sales Navigator that uses AI to intrepret user queries and provide relevant search results.
- Kiwi - AI assisted message generation feature in LinkedIn Sales Navigator that helps users draft messages to prospects.
- Catalyst BE team - A tiger team selected from the LinkedIn Sales Solutions team to work on high-impact projects, such as the Account IQ. BE stands for backend
- BE stack - Backend stack, using Java and Espresso database, involving Temporal for scheduled jobs
- sales-api - the internal codebase name for the API layer for LinkedIn Sales Solutions, which serves as the backend for various features and functionalities in Sales Navigator.
- lss-gai-mt - LinkedIn Sales Solutions Generative AI Mid-Tier, a mid-tier service that handles business logic for generative AI features in Sales Navigator.
- mp (context: "the new mp is now the source of truth") - Multiproduct, another name for deployable repository, which is the source of truth for the codebase and deployment of LinkedIn Sales Solutions.
- WAU (context: "WAU OKR for AIQ") - Weekly Active Users, a key performance indicator used to measure the engagement and success of features like Account IQ.
- OKR - Objectives and Key Results, a goal-setting framework used to define and track objectives and their outcomes.
- PRD - Product Requirements Document, a document that outlines the requirements and specifications for a product or feature.
- CRM badge and writeback dialog - Features in LinkedIn Sales Navigator that allow users to write back information to their CRM systems, such as Salesforce or Microsoft Dynamics CRM. Badge refers to the visual indicator in the UI, to indicate if a lead was linked to a LinkedIn profile, and writeback status.
- CRM (context: "Salesforce or Microsoft Dynamics CRM")
- Smart Link Viewer - A complete reubild of pointdrive that LSS acquired in 2016, used by sales professionals to share content with prospects and track engagement. That involves designing a hearbeat tracking system, offline processing, and a new UI for viewing shared content.
- Teamlink onboarding - Teamlink is a feature in LinkedIn Sales Navigator that allows users to leverage their network to find connections to prospects. Onboarding refers to the process of integrating and setting up this feature for new users.
- Teamlink Spotlight - Spotlights refers to badges shown on the search; Teamlink spotlight allows sales to see insights about their prospects based on their network connections, such as mutual connections or shared interests.
- glint - LinkedIn's internal employee engagement and feedback platform, used to gather insights and improve team dynamics.
- product list picker - A highly reusabel UI component to allow sales to pick the product they represent when doing leads prospecting
- expandable line clamper - The UI component that allows users to expand or collapse long lines of text in the UI, such as in lead descriptions or summaries.
- grid helper - A utility or component used in the UI to assist with layout and grid management, ensuring consistent spacing and alignment of elements.
- Impression UU / Action UU - UU = unique user, Impression UU refers to the number of unique users who have viewed a particular feature or content, while Action UU refers to the number of unique users who have taken a specific action, such as clicking or interacting with a feature.
- LIX key - LIX means LinkedIn Experiment, and LIX key refers to a unique identifier for an experiment or test conducted within LinkedIn's platform, often used to track and analyze the results of A/B tests or feature rollouts.
- i18n - Internationalization, the process of adapting software or content to different languages and regions, ensuring that it is accessible and usable by a global audience.
- retina (context: "retina, metrics") - Used internally in LinkedIn to measure key business metrics
- RFC - Request for Comments, a type of document used to propose and discuss new features, standards, or changes within a project or organization, often serving as a formal way to gather feedback and reach consensus.
- Prompt IDE - A tool I initiated aiming to help product manager to iterate on prompts for generative AI features, allowing them to test and refine prompts in a user-friendly interface powered by Steamlit, which is a Python-based framework for building interactive web applications.
- Langsmith vs. lss-gai-ui - lss-gai-ui is the internal codename for Prompt IDe
- Shadow DOM - hidden DOM tree that is attached to an element, allowing for encapsulation of styles and scripts, commonly used in web components to create reusable UI elements without affecting the global scope.
- Monorepo - A software development strategy where all code for multiple projects is stored in a single repository, allowing for easier management, version control, and collaboration across teams.
- Storybook - React component development environment and documentation tool, allowing developers to create, test, and showcase UI components in isolation.
- a11y - Accessibility, ensuring that software and web applications are usable by people with disabilities, including those who rely on assistive technologies.
- Pull Request (PR)
- ticket (context: "ticket, PRD")
- lead panel - the UI component on search page, for users to define search criteria
- E2E - End to End, referring to testing or processes that cover the entire workflow from start to finish, ensuring that all components work together as intended.
- runbook - a document that provides step-by-step instructions for performing a specific task or process, often used in IT operations and incident response to ensure consistency and efficiency.